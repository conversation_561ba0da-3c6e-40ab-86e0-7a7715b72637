import { IsArray, IsOptional, IsString, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateAppDto {
	@ApiProperty({
		description: 'Unique application key in lowercase kebab-case format',
		example: 'my-awesome-app',
		pattern: '^[a-z0-9]+(?:-[a-z0-9]+)*$'
	})
	@IsString()
	@Matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/) // lowercase kebab
	key!: string;

	@ApiProperty({
		description: 'Human-readable application name',
		example: 'My Awesome App'
	})
	@IsString()
	name!: string;

	@ApiProperty({
		description: 'Optional application description',
		example: 'This is an awesome application that does amazing things',
		required: false
	})
	@IsOptional() @IsString()
	description?: string;

	@ApiProperty({
		description: 'Optional URL to application logo',
		example: 'https://example.com/logo.png',
		required: false
	})
	@IsOptional() @IsString()
	logoUrl?: string;

	@ApiProperty({
		description: 'Optional external application identifier',
		example: 'ext-app-123',
		required: false
	})
	@IsOptional() @IsString()
	externalAppId?: string;

	@ApiProperty({
		description: 'Array of OAuth scopes required by the application',
		example: ['read:users', 'write:data'],
		type: [String],
		default: []
	})
	@IsArray()
	scopes: string[] = [];
}

export class UpdateAppDto {
	@ApiProperty({
		description: 'Updated application name',
		example: 'My Updated App Name',
		required: false
	})
	@IsOptional()
	@IsString()
	name?: string;

	@ApiProperty({
		description: 'Updated OAuth scopes',
		example: ['read:users', 'write:data', 'admin:all'],
		type: [String],
		required: false
	})
	@IsOptional()
	@IsArray()
	scopes?: string[];
}
