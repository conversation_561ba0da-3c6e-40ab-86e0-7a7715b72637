import { Injectable, CanActivate, ExecutionContext, UnauthorizedException, ForbiddenException } from '@nestjs/common';
import { AuthService } from '../auth.service';

export { GraphQLAuthGuard } from './graphql-auth.guard';

@Injectable()
export class ApiGuard implements CanActivate {
  constructor(private authService: AuthService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    try {
      const cookies = (request as any).cookies || {};
      
      const user = await this.authService.authenticateFromCookies(cookies);
      
      request.user = user;
      
      return true;
    } catch (error) {
      throw new UnauthorizedException('Authentication required');
    }
  }
}

@Injectable()
export class AdminGuard implements CanActivate {
  constructor(private authService: AuthService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    try {
      const cookies = (request as any).cookies || {};
      
      const user = await this.authService.authenticateFromCookies(cookies);
      
      if (!this.authService.hasRole(user, 'admin')) {
        throw new ForbiddenException('Admin access required');
      }
      
      request.user = user;
      
      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new UnauthorizedException('Authentication required');
    }
  }
}
