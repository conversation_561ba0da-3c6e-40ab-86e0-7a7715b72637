import { IsEnum, IsInt, IsOptional, IsString, Matches, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateEndpointDto {
	@ApiProperty({
		description: 'Unique endpoint key within the version',
		example: 'get-user-profile'
	})
	@IsString()
	key!: string;

	@ApiProperty({
		description: 'HTTP method for the endpoint',
		example: 'GET',
		enum: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE']
	})
	@IsString()
	method!: string;

	@ApiProperty({
		description: 'Path template for the endpoint (must start with /)',
		example: '/users/{userId}/profile'
	})
	@IsString()
	@Matches(/^\//)
	pathTemplate!: string;

	@ApiProperty({
		description: 'Optional headers template',
		example: { 'Content-Type': 'application/json' },
		required: false
	})
	@IsOptional()
	headersTemplate?: Record<string, any>;

	@ApiProperty({
		description: 'Optional query parameters template',
		example: { 'include': 'profile,settings' },
		required: false
	})
	@IsOptional()
	queryTemplate?: Record<string, any>;

	@ApiProperty({
		description: 'Optional request body template with Handlebars tokens',
		example: '{"userId": "{{userId}}", "data": "{{requestData}}"}',
		required: false
	})
	@IsOptional()
	bodyTemplate?: string;

	@ApiProperty({
		description: 'Request timeout in milliseconds',
		example: 30000,
		minimum: 1000,
		maximum: 60000,
		required: false
	})
	@IsOptional()
	@IsInt()
	@Min(1000)
	@Max(60000)
	timeoutMs?: number;

	@ApiProperty({
		description: 'Maximum number of retry attempts',
		example: 3,
		minimum: 0,
		maximum: 5,
		required: false
	})
	@IsOptional()
	@IsInt()
	@Min(0)
	@Max(5)
	retryMax?: number;

	@ApiProperty({
		description: 'Retry backoff strategy',
		example: 'EXPONENTIAL',
		enum: ['EXPONENTIAL', 'LINEAR', 'FIXED'],
		required: false
	})
	@IsOptional()
	@IsString()
	retryBackoff?: string;

	@ApiProperty({
		description: 'Initial retry delay in milliseconds',
		example: 1000,
		required: false
	})
	@IsOptional()
	@IsInt()
	retryInitialMs?: number;
}
// import { BackoffStrategy, HttpMethod } from '@prisma/client';
