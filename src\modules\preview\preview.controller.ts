import { BadRequestException, Body, Controller, Post } from '@nestjs/common';
import Handlebars from 'handlebars';

@Controller('validate')
export class PreviewController {
@Post('template')
preview(@Body() dto: { bodyTemplate?: string; input?: any }) {
const { bodyTemplate, input } = dto || {};
if (!bodyTemplate) return { rendered: null, sizeBytes: 0 };
try {
const compiled = Handlebars.compile(bodyTemplate, { noEscape: true });
const renderedStr = compiled(input ?? {});
const rendered = JSON.parse(renderedStr);
const sizeBytes = Buffer.byteLength(renderedStr, 'utf8');
if (sizeBytes > 256 * 1024) throw new BadRequestException('rendered_too_large');
return { rendered, sizeBytes };
} catch (e: any) {
throw new BadRequestException({ code: 'template_render_error', message: e.message });
}
}
}