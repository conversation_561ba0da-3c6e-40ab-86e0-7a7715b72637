// VersionService: handles version management logic
import { Injectable } from '@nestjs/common';
import { CreateVersionDto, ApproveVersionDto, SubmitVersionDto, VersionEnvConfigDto } from './dto/version.dto';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class VersionService {
  constructor(private readonly prisma: PrismaService) {}

  async findOne(appId: string, versionId: string) {
    return this.prisma.appVersion.findFirst({ where: { id: versionId, appId } });
  }

  async createVersion(appId: string, dto: CreateVersionDto) {
    const app = await this.prisma.app.findUnique({ where: { id: appId } });
    if (!app) throw new Error('app_not_found');
    const version = await this.prisma.appVersion.create({
      data: {
        appId,
        version: dto.version,
        authType: dto.authType,
        oauthScopes: dto.oauthScopes,
        notes: dto.notes,
      },
    });
    if (dto.envs?.length) {
      await this.prisma.versionEnvironment.createMany({
        data: dto.envs.map((e: VersionEnvConfigDto) => ({ ...e, versionId: version.id })),
      });
    }
    return version;
  }

  async submitVersion(appId: string, versionId: string, actor: string) {
    const v = await this.prisma.appVersion.findFirst({ where: { id: versionId, appId }, include: { envConfigs: true, endpoints: true } });
    if (!v) throw new Error('version_not_found');
    if (!v.envConfigs.some((e: { env: string }) => e.env === 'SANDBOX')) throw new Error('sandbox_env_required');
    if (v.endpoints.length === 0) throw new Error('endpoints_required');
    return this.prisma.appVersion.update({ where: { id: v.id }, data: { status: 'APPROVED', submittedBy: actor, submittedAt: new Date() } });
  }

  async approveVersion(appId: string, versionId: string, actor: string) {
    const v = await this.prisma.appVersion.findFirst({ where: { id: versionId, appId } });
    if (!v) throw new Error('version_not_found');
    const updated = await this.prisma.appVersion.update({ where: { id: v.id }, data: { status: 'APPROVED', approvedBy: actor, approvedAt: new Date() } });
    return updated;
  }

  async deprecateVersion(appId: string, versionId: string) {
    return this.prisma.appVersion.update({ where: { id: versionId }, data: { status: 'DEPRECATED', deprecatedAt: new Date() } });
  }
}
