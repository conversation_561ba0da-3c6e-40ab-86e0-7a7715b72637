import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { AuthService } from '../auth.service';

@Injectable()
export class GraphQLAuthGuard implements CanActivate {
  constructor(private authService: AuthService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const gqlContext = GqlExecutionContext.create(context);
    const { req } = gqlContext.getContext();
    
    try {
      const cookies = req.cookies || {};
      
      const user = await this.authService.authenticateFromCookies(cookies);
      
      req.user = user;
      
      return true;
    } catch (error) {
      throw new UnauthorizedException('Authentication required');
    }
  }
}
