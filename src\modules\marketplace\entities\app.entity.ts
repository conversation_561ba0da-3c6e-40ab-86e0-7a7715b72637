import { ObjectType, Field, ID } from '@nestjs/graphql';

@ObjectType({
  description: 'Marketplace application entity'
})
export class App {
  @Field(() => ID, {
    description: 'Unique application identifier'
  })
  id!: string;

  @Field(() => String, {
    description: 'Application key in kebab-case format'
  })
  key!: string;

  @Field(() => String, {
    description: 'Human-readable application name'
  })
  name!: string;

  @Field(() => String, {
    description: 'Application description',
    nullable: true
  })
  description?: string;

  @Field(() => String, {
    description: 'URL to application logo',
    nullable: true
  })
  logoUrl?: string;

  @Field(() => String, {
    description: 'External application identifier',
    nullable: true
  })
  externalAppId?: string;

  @Field(() => [String], {
    description: 'OAuth scopes required by the application',
    nullable: true
  })
  scopes?: string[];

  @Field(() => Date, {
    description: 'Application creation timestamp'
  })
  createdAt!: Date;

  @Field(() => Date, {
    description: 'Application last update timestamp'
  })
  updatedAt!: Date;
}
