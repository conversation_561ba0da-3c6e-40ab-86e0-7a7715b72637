// Additional GraphQL DTOs from marketplace module
import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class CreateAppGql {
  @Field()
  key!: string;

  @Field()
  name!: string;

  @Field({ nullable: true })
  description?: string;

  @Field({ nullable: true })
  logoUrl?: string;

  @Field({ nullable: true })
  externalAppId?: string;

  @Field(() => [String], { nullable: true })
  scopes?: string[];
}
