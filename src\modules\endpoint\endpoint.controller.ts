import { <PERSON>, <PERSON>, Param, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody, ApiBadRequestResponse, ApiNotFoundResponse } from '@nestjs/swagger';
import { EndpointService } from './endpoint.service';
import { CreateEndpointDto } from './dto/create-endpoint.dto';

@ApiTags('Endpoints')
@Controller('apps/:appId/versions/:versionId/endpoints')
export class EndpointController {
  constructor(private readonly endpointService: EndpointService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new endpoint',
    description: 'Creates a new API endpoint for the specified application version'
  })
  @ApiParam({
    name: 'appId',
    description: 'Application ID'
  })
  @ApiParam({
    name: 'versionId',
    description: 'Version ID'
  })
  @ApiBody({ type: CreateEndpointDto })
  @ApiResponse({
    status: 201,
    description: 'Endpoint created successfully'
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data'
  })
  @ApiNotFoundResponse({
    description: 'Application or version not found'
  })
  createEndpoint(
    @Param('appId') appId: string,
    @Param('versionId') versionId: string,
    @Body() dto: CreateEndpointDto
  ) {
    return this.endpointService.createEndpoint(appId, versionId, dto);
  }
}
