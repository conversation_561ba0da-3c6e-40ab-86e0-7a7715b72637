import { Body, Controller, Get, Param, Post, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody, ApiBadRequestResponse, ApiNotFoundResponse } from '@nestjs/swagger';
import { VersionService } from './version.service';
import { CreateVersionDto, ApproveVersionDto, SubmitVersionDto, VersionEnvConfigDto } from './dto/version.dto';

@ApiTags('Versions')
@Controller('apps/:appId/versions')
export class VersionsController {
  constructor(private svc: VersionService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new application version',
    description: 'Creates a new version for the specified application'
  })
  @ApiParam({
    name: 'appId',
    description: 'Application ID'
  })
  @ApiBody({ type: CreateVersionDto })
  @ApiResponse({
    status: 201,
    description: 'Version created successfully'
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data'
  })
  @ApiNotFoundResponse({
    description: 'Application not found'
  })
  create(@Param('appId') appId: string, @Body() dto: CreateVersionDto) {
    return this.svc.createVersion(appId, dto);
  }

  @Get(':versionId')
  get(@Param('appId') appId: string, @Param('versionId') id: string) {
    return this.svc.findOne(appId, id);
  }

  @Post(':versionId/submit')
  submit(@Param('appId') appId: string, @Param('versionId') id: string, @Req() req: any) {
    return this.svc.submitVersion(appId, id, req.user?.sub || 'system');
  }

  @Post(':versionId/approve')
  approve(@Param('appId') appId: string, @Param('versionId') id: string, @Req() req: any) {
    return this.svc.approveVersion(appId, id, req.user?.sub || 'system');
  }

  @Post(':versionId/deprecate')
  deprecate(@Param('appId') appId: string, @Param('versionId') id: string) {
    return this.svc.deprecateVersion(appId, id);
  }
}
