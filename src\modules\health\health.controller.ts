import { Controller, Get, HttpException, HttpStatus } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';


@Controller()
export class HealthController {
constructor(private prisma: PrismaService) {}


@Get('/health')
health() {
return { ok: true };
}


@Get('/ready')
async ready() {
try {
await this.prisma.$queryRaw`SELECT 1`;
return { ok: true };
} catch (e) {
throw new HttpException({ ok: false, error: 'db_unavailable' }, HttpStatus.SERVICE_UNAVAILABLE);
}
}
}