import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { GraphQLModule } from '@nestjs/graphql';
import { MercuriusDriver, MercuriusDriverConfig } from '@nestjs/mercurius';
import { join } from 'path';
import configuration from './config/configuration';
import { envValidationSchema } from './config/validation';
import { PrismaModule } from './prisma/prisma.module';
import { HealthModule } from './modules/health/health.module';
import { MarketplaceModule } from './modules/marketplace/marketplace.module';
import { VersionModule } from './modules/version/version.module';
import { EndpointModule } from './modules/endpoint/endpoint.module';
import { AuthModule } from './modules/auth/auth.module';


@Module({
imports: [
ConfigModule.forRoot({
isGlobal: true,
load: [configuration],
validationSchema: envValidationSchema,
validationOptions: { abortEarly: false },
}),
GraphQLModule.forRoot<MercuriusDriverConfig>({
  driver: MercuriusDriver,
  autoSchemaFile: join(process.cwd(), 'schema.gql'),
  sortSchema: true,
  graphiql: {
    enabled: true,
  },
  ide: true,
  path: '/graphql',
  context: (request: any, reply: any) => ({ req: request, reply }),
  subscription: true,
}),
AuthModule,
HealthModule,
PrismaModule,
MarketplaceModule,
VersionModule,
EndpointModule,
],
})
export class AppModule {}