import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class MarketplaceService {
  constructor(private readonly prisma: PrismaService) {}

  async create(dto: any) {
    return this.prisma.app.create({ data: dto });
  }

  async list(cursor?: string, limit = 20) {
    return this.prisma.app.findMany({
      skip: cursor ? 1 : 0,
      take: limit,
      ...(cursor && { cursor: { id: cursor } })
    });
  }

  async get(id: string) {
    return this.prisma.app.findUnique({ where: { id } });
  }

  async update(id: string, dto: any) {
    return this.prisma.app.update({ where: { id }, data: dto });
  }
}
