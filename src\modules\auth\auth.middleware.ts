import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { FastifyRequest, FastifyReply } from 'fastify';
import { AuthService } from './auth.service';
import { User } from './types/auth.types';

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  private readonly logger = new Logger(AuthMiddleware.name);

  constructor(private authService: AuthService) {}

  async use(req: FastifyRequest, res: FastifyReply, next: Function) {
    try {
      const path = req.url || '';
      if (this.isPublicRoute(path)) {
        return next();
      }

      let cookies = (req as any).cookies || {};

      if (Object.keys(cookies).length === 0) {
        const cookieHeader = (req as any).headers?.cookie;
        if (cookieHeader) {
          this.logger.debug(`Parsing cookies from header: ${cookieHeader.substring(0, 100)}...`);
          cookies = this.parseCookieHeader(cookieHeader);
        }
      }

      this.logger.debug(`Middleware extracted cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

      try {
        const user = await this.authService.authenticateFromCookies(cookies);
        (req as any).user = user;
        this.logger.debug(`User authenticated: ${user.email || user.sub}`);
      } catch (error) {
        this.logger.debug('Authentication failed in middleware:', error instanceof Error ? error.message : String(error));
      }

      next();
    } catch (error) {
      this.logger.error('Auth middleware error:', error);
      next();
    }
  }

  private isPublicRoute(path: string): boolean {
    if (!path) return false;

    const publicRoutes = [
      '/health',
      '/api/health',
      '/api/auth/login',
      '/api/auth/logout',
      '/api/auth/callback',
      '/graphql',
    ];

    return publicRoutes.some(route => path.startsWith(route));
  }

  private parseCookieHeader(cookieHeader: string): Record<string, string> {
    const cookies: Record<string, string> = {};

    if (!cookieHeader) return cookies;

    cookieHeader.split(';').forEach(cookie => {
      const [name, ...rest] = cookie.trim().split('=');
      if (name && rest.length > 0) {
        const value = rest.join('=');
        cookies[name] = decodeURIComponent(value);
      }
    });

    return cookies;
  }
}

export function authMiddleware(authService: AuthService) {
  return async (req: FastifyRequest, res: FastifyReply, next: Function) => {
    try {
      const cookies = (req as any).cookies || {};

      try {
        const user = await authService.authenticateFromCookies(cookies);
        (req as any).user = user;
      } catch (error) {
        // User not authenticated, but don't block the request
      }

      next();
    } catch (error) {
      console.error('Auth middleware error:', error);
      next();
    }
  };
}
