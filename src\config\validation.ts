import * as <PERSON><PERSON> from 'joi';


export const envValidationSchema = Joi.object({
NODE_ENV: Joi.string().valid('development', 'test', 'production', 'staging').default('dev'),
PORT: Joi.number().min(1).max(65535).default(3050),
DATABASE_URL: Joi.string().uri().required(),
AUTH_ISSUER: Joi.string().uri().optional(),
JWT_ISSUER_URL: Joi.string().uri().optional(),
AUTH_AUDIENCE: Joi.string().default('ngnair'),
JWT_AUDIENCE: Joi.string().optional(),
AUTH_JWKS_URI: Joi.string().uri().optional(),
AUTH_JWKS_URL: Joi.string().uri().optional(),
AUTH_SERVICE_URL: Joi.string().uri().optional(),
ACCESS_TOKEN_ENCRYPTION_KEY: Joi.string().optional(),
JWT_SKIP_VERIFICATION: Joi.string().valid('true', 'false').optional(),
MASTER_KEY: Joi.string().min(16).required(),
});