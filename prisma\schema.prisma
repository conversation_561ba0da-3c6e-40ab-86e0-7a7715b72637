generator client {
provider = "prisma-client-js"
}


datasource db {
provider = "postgresql"
url = env("DATABASE_URL")
}


enum VersionStatus {
  DRAFT
  APPROVED
  DEPRECATED
}


enum SubjectType {
  ACCOUNT
  PARTNER
}


enum Environment {
  SANDBOX
  PROD
}


model App {
id String @id @default(cuid())
key String @unique
name String
createdAt DateTime @default(now())
updatedAt DateTime @updatedAt
versions AppVersion[]
}


model AppVersion {
id String @id @default(cuid())
appId String
appInstalls AppInstall[]
app App @relation(fields: [appId], references: [id])
version String
status VersionStatus @default(DRAFT)
notes String?
authType String
oauthScopes String[]
envConfigs VersionEnvironment[]
endpoints EndpointDefinition[]
webhooks WebhookRoute[]
submittedBy String?
submittedAt DateTime?
approvedBy String?
approvedAt DateTime?
deprecatedAt DateTime?
createdAt DateTime @default(now())
updatedAt DateTime @updatedAt
@@unique([appId, version])
@@index([status])
}
model VersionEnvironment {
  id String @id @default(cuid())
  versionId String
  version AppVersion @relation(fields: [versionId], references: [id])
  env Environment
  baseUrl String
  tokenUrl String?
  audience String?
  additionalAuth Json?
  defaultHeaders Json?
  defaultQuery Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  @@unique([versionId, env])
}
model EndpointDefinition {
  id String @id @default(cuid())
  versionId String
  version AppVersion @relation(fields: [versionId], references: [id])
  key String
  method String
  pathTemplate String
  headersTemplate Json?
  queryTemplate Json?
  bodyTemplate String?
  templateDialect String @default("HANDLEBARS")
  timeoutMs Int?
  retryMax Int?
  retryBackoff String @default("EXPONENTIAL")
  retryInitialMs Int?
  onSuccess Json?
  onFailure Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  @@unique([versionId, key])
  @@index([key])
}
model WebhookRoute {
  id String @id @default(cuid())
  versionId String
  version AppVersion @relation(fields: [versionId], references: [id])
  routePath String
  signatureScheme String
  dedupeHeader String?
}


model AppInstall {
id String @id @default(cuid())
appVersionId String
appVersion AppVersion @relation(fields: [appVersionId], references: [id])
subjectType SubjectType
subjectId String
environment Environment @default(SANDBOX)
scopes String[]
createdAt DateTime @default(now())
}

model HealthCheck {
id String @id @default(cuid())
createdAt DateTime @default(now())
}