import { ApiProperty } from '@nestjs/swagger';

export class UserResponseDto {
  @ApiProperty({
    description: 'JWT issuer URL',
    example: 'https://ng-auth-dev.dev1.ngnair.com'
  })
  iss!: string;

  @ApiProperty({
    description: 'User unique identifier (subject)',
    example: 'f7b98e6f-95af-4d54-9c53-312ada49ba6e'
  })
  sub!: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>'
  })
  email!: string;

  @ApiProperty({
    description: 'JWT audience',
    example: 'ngnair'
  })
  aud!: string;

  @ApiProperty({
    description: 'Token expiration timestamp',
    example: 1758865885
  })
  exp!: number;

  @ApiProperty({
    description: 'Token issued at timestamp',
    example: 1758779485
  })
  iat!: number;

  @ApiProperty({
    description: 'JWT ID',
    example: 'ef903136-d996-49be-b516-8bd7560e8d75'
  })
  jti!: string;

  @ApiProperty({
    description: 'Session ID',
    example: 'sess_5b3502f20fa0a09d'
  })
  sid!: string;

  @ApiProperty({
    description: 'Authorized party',
    example: 'webapp'
  })
  azp!: string;

  @ApiProperty({
    description: 'Entity set identifier',
    example: 'e_de2e18a'
  })
  ent_set!: string;

  @ApiProperty({
    description: 'Permission version',
    example: 0
  })
  perm_v!: number;

  @ApiProperty({
    description: 'Authentication methods references',
    example: ['pwd'],
    type: [String]
  })
  amr!: string[];

  @ApiProperty({
    description: 'Authentication time timestamp',
    example: 1758779485
  })
  auth_time!: number;
}

export class UsersResponseDto {
  @ApiProperty({
    description: 'Array of users',
    type: [UserResponseDto]
  })
  users!: UserResponseDto[];

  @ApiProperty({
    description: 'Total count of users',
    example: 1
  })
  total!: number;
}

export class ErrorResponseDto {
  @ApiProperty({
    description: 'HTTP status code',
    example: 401
  })
  statusCode!: number;

  @ApiProperty({
    description: 'Error message',
    example: 'Unauthorized'
  })
  message!: string;

  @ApiProperty({
    description: 'Error type',
    example: 'Unauthorized'
  })
  error!: string;

  @ApiProperty({
    description: 'Timestamp of the error',
    example: '2023-12-01T10:00:00.000Z'
  })
  timestamp!: string;

  @ApiProperty({
    description: 'Request path',
    example: '/auth/me'
  })
  path!: string;
}
