import { InputType, Field } from '@nestjs/graphql';
import { IsArray, IsOptional, IsString, Matches } from 'class-validator';

@InputType({
  description: 'Input for creating a new marketplace application'
})
export class CreateAppInput {
  @Field(() => String, {
    description: 'Unique application key in lowercase kebab-case format'
  })
  @IsString()
  @Matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
  key!: string;

  @Field(() => String, {
    description: 'Human-readable application name'
  })
  @IsString()
  name!: string;

  @Field(() => String, {
    description: 'Optional application description',
    nullable: true
  })
  @IsOptional()
  @IsString()
  description?: string;

  @Field(() => String, {
    description: 'Optional URL to application logo',
    nullable: true
  })
  @IsOptional()
  @IsString()
  logoUrl?: string;

  @Field(() => String, {
    description: 'Optional external application identifier',
    nullable: true
  })
  @IsOptional()
  @IsString()
  externalAppId?: string;

  @Field(() => [String], {
    description: 'Array of OAuth scopes required by the application',
    defaultValue: []
  })
  @IsArray()
  scopes!: string[];
}

@InputType({
  description: 'Input for updating an existing marketplace application'
})
export class UpdateAppInput {
  @Field(() => String, {
    description: 'Updated application name',
    nullable: true
  })
  @IsOptional()
  @IsString()
  name?: string;

  @Field(() => [String], {
    description: 'Updated OAuth scopes',
    nullable: true
  })
  @IsOptional()
  @IsArray()
  scopes?: string[];
}
