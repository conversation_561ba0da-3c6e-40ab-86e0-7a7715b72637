import { FastifyInstance } from 'fastify';


export default async function runtimeRoutes(app: FastifyInstance) {
app.post('/runtime/invoke/:installId/:endpointKey', async (req, reply) => {
const { installId, endpointKey } = req.params as { installId: string; endpointKey: string };
const body = req.body as Record<string, unknown>;


// Minimal stub: fetch install + version + endpoint
const install = await app.prisma.appInstall.findUnique({
where: { id: installId },
include: { appVersion: true }
});
// Use reply.status for notFound
if (!install) return reply.status(404).send({ message: 'Install not found' });


// For now, echo back what would be executed
return reply.send({
ok: true,
resolved: {
environment: install.environment,
endpointKey,
payload: body,
appVersionId: install.appVersionId
}
});
});
}