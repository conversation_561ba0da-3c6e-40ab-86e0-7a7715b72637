-- Create Enums
CREATE TYPE "VersionStatus" AS ENUM ('DRAFT', 'APPROVED', 'DEPRECATED');
CREATE TYPE "SubjectType" AS ENUM ('ACCOUNT', 'PARTNER');
CREATE TYPE "Environment" AS ENUM ('SANDBOX', 'PROD');

-- Create Tables
CREATE TABLE "App" (
  "id" VARCHAR(25) PRIMARY KEY,
  "key" VARCHAR(255) UNIQUE NOT NULL,
  "name" VARCHAR(255) NOT NULL,
  "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE "AppVersion" (
  "id" VARCHAR(25) PRIMARY KEY,
  "appId" VARCHAR(25) NOT NULL,
  "version" VARCHAR(255) NOT NULL,
  "status" "VersionStatus" NOT NULL DEFAULT 'DRAFT',
  "notes" TEXT,
  "authType" VARCHAR(255) NOT NULL,
  "oauthScopes" TEXT[],
  "submittedBy" VARCHAR(255),
  "submittedAt" TIMESTAMP,
  "approvedBy" VARCHAR(255),
  "approvedAt" TIMESTAMP,
  "deprecatedAt" TIMESTAMP,
  "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT "AppVersion_appId_fkey" FOREIGN KEY ("appId") REFERENCES "App"("id"),
  CONSTRAINT "AppVersion_appId_version_key" UNIQUE ("appId", "version")
);

CREATE INDEX "AppVersion_status_idx" ON "AppVersion" ("status");

CREATE TABLE "VersionEnvironment" (
  "id" VARCHAR(25) PRIMARY KEY,
  "versionId" VARCHAR(25) NOT NULL,
  "env" "Environment" NOT NULL,
  "baseUrl" VARCHAR(255) NOT NULL,
  "tokenUrl" VARCHAR(255),
  "audience" VARCHAR(255),
  "additionalAuth" JSON,
  "defaultHeaders" JSON,
  "defaultQuery" JSON,
  "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT "VersionEnvironment_versionId_fkey" FOREIGN KEY ("versionId") REFERENCES "AppVersion"("id"),
  CONSTRAINT "VersionEnvironment_versionId_env_key" UNIQUE ("versionId", "env")
);

CREATE TABLE "EndpointDefinition" (
  "id" VARCHAR(25) PRIMARY KEY,
  "versionId" VARCHAR(25) NOT NULL,
  "key" VARCHAR(255) NOT NULL,
  "method" VARCHAR(255) NOT NULL,
  "pathTemplate" VARCHAR(255) NOT NULL,
  "headersTemplate" JSON,
  "queryTemplate" JSON,
  "bodyTemplate" TEXT,
  "templateDialect" VARCHAR(255) NOT NULL DEFAULT 'HANDLEBARS',
  "timeoutMs" INT,
  "retryMax" INT,
  "retryBackoff" VARCHAR(255) NOT NULL DEFAULT 'EXPONENTIAL',
  "retryInitialMs" INT,
  "onSuccess" JSON,
  "onFailure" JSON,
  "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT "EndpointDefinition_versionId_fkey" FOREIGN KEY ("versionId") REFERENCES "AppVersion"("id"),
  CONSTRAINT "EndpointDefinition_versionId_key_key" UNIQUE ("versionId", "key")
);

CREATE INDEX "EndpointDefinition_key_idx" ON "EndpointDefinition" ("key");

CREATE TABLE "WebhookRoute" (
  "id" VARCHAR(25) PRIMARY KEY,
  "versionId" VARCHAR(25) NOT NULL,
  "routePath" VARCHAR(255) NOT NULL,
  "signatureScheme" VARCHAR(255) NOT NULL,
  "dedupeHeader" VARCHAR(255),
  CONSTRAINT "WebhookRoute_versionId_fkey" FOREIGN KEY ("versionId") REFERENCES "AppVersion"("id")
);

CREATE TABLE "AppInstall" (
  "id" VARCHAR(25) PRIMARY KEY,
  "appVersionId" VARCHAR(25) NOT NULL,
  "subjectType" "SubjectType" NOT NULL,
  "subjectId" VARCHAR(255) NOT NULL,
  "environment" "Environment" NOT NULL DEFAULT 'SANDBOX',
  "scopes" TEXT[],
  "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT "AppInstall_appVersionId_fkey" FOREIGN KEY ("appVersionId") REFERENCES "AppVersion"("id")
);

CREATE TABLE "HealthCheck" (
  "id" VARCHAR(25) PRIMARY KEY,
  "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
