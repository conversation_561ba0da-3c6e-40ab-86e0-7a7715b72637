import { ObjectType, Field, Int } from '@nestjs/graphql';

@ObjectType({
  description: 'User entity representing authenticated user information from JWT token'
})
export class User {
  @Field(() => String, {
    description: 'JWT issuer URL'
  })
  iss!: string;

  @Field(() => String, {
    description: 'User unique identifier (subject)'
  })
  sub!: string;

  @Field(() => String, {
    description: 'User email address',
    nullable: true
  })
  email?: string;

  @Field(() => String, {
    description: 'JWT audience'
  })
  aud!: string;

  @Field(() => Int, {
    description: 'Token expiration timestamp'
  })
  exp!: number;

  @Field(() => Int, {
    description: 'Token issued at timestamp'
  })
  iat!: number;

  @Field(() => String, {
    description: 'JWT ID'
  })
  jti!: string;

  @Field(() => String, {
    description: 'Session ID'
  })
  sid!: string;

  @Field(() => String, {
    description: 'Authorized party'
  })
  azp!: string;

  @Field(() => String, {
    description: 'Entity set identifier'
  })
  ent_set!: string;

  @Field(() => Int, {
    description: 'Permission version'
  })
  perm_v!: number;

  @Field(() => [String], {
    description: 'Authentication methods references'
  })
  amr!: string[];

  @Field(() => Int, {
    description: 'Authentication time timestamp'
  })
  auth_time!: number;
}
