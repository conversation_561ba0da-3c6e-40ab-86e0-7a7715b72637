import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import helmet from '@fastify/helmet';
import cors from '@fastify/cors';
import rateLimit from '@fastify/rate-limit';
import cookie from '@fastify/cookie';
import env from './plugins/env';
import sensible from './plugins/sensible';
import { AppModule } from './app.module';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';

async function bootstrap() {
  // Determine environment
  const isDevOrTest = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';

  // Detect if running in a containerized environment
  const isContainerized = process.env.CONTAINER_ENV === 'true' ||
                          process.env.DOCKER === 'true' ||
                          process.env.KUBERNETES_SERVICE_HOST ||
                          process.env.HOSTNAME?.startsWith('srv-captain') ||
                          process.env.NODE_ENV === 'production';

  // In containerized environments, always bind to 0.0.0.0 to accept connections from any interface
  // In local development, use SOURCE_IP if provided, otherwise default to 127.0.0.1
  const sourceIp = isContainerized ? '0.0.0.0' : (process.env.SOURCE_IP || '127.0.0.1');

  const port = process.env.PORT ? Number(process.env.PORT) : 3000;
  const listen_port = process.env.LISTENING_PORT ? Number(process.env.LISTENING_PORT) : port;

  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter({ logger: false })
  );

  await app.register(helmet as any);
  await app.register(cors as any, {
    origin: isDevOrTest ? `http://localhost:${port}` : 'https://your.production.frontend',
    credentials: true,
  });
  await app.register(rateLimit as any, { max: 200, timeWindow: '1 minute' });
  await app.register(cookie as any);
  await app.register(env as any);
  await app.register(sensible as any);

  app.useGlobalInterceptors(new LoggingInterceptor());
  app.useGlobalPipes(new ValidationPipe({ transform: true, whitelist: true }));

  // Configure Swagger/OpenAPI with dynamic server detection
  const config = new DocumentBuilder()
    .setTitle('Marketplace Backend API')
    .setDescription('Comprehensive API documentation for the Marketplace Backend application')
    .setVersion('1.0')
    .addCookieAuth('access_token', {
      type: 'apiKey',
      in: 'cookie',
      name: 'access_token',
      description: 'Authentication cookie containing encrypted access token'
    })
    .addTag('Authentication', 'User authentication and authorization endpoints')
    .addTag('Marketplace', 'Marketplace application management')
    .addTag('Versions', 'Application version management')
    .addTag('Endpoints', 'API endpoint configuration')
    .addTag('Health', 'Health check endpoints')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      docExpansion: 'none',
      filter: true,
      showRequestDuration: true,
      tryItOutEnabled: true,
      // Use relative URLs so Swagger automatically uses the current domain
      url: '/api-json',
    },
    customSiteTitle: 'Marketplace API Documentation',
    customfavIcon: '/favicon.ico',
    customJs: [
      'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui-bundle.min.js',
      'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui-standalone-preset.min.js',
    ],
    customCssUrl: [
      'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui.min.css',
    ],
  });

  await app.listen(listen_port, sourceIp);

  console.log(`🚀 Application is running on: http://ng-marketplace-local.dev.dev1.ngnair.com:${listen_port}`);
  console.log(`📚 Swagger UI available at: http://ng-marketplace-local.dev.dev1.ngnair.com:${listen_port}/api`);
  console.log(`🎯 GraphQL Playground available at: http://ng-marketplace-local.dev.dev1.ngnair.com:${listen_port}/graphql`);
}
bootstrap();