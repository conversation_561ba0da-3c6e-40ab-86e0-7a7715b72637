import fp from 'fastify-plugin';
import { PrismaClient } from '@prisma/client';


export default fp(async (app) => {
const prisma = new PrismaClient();
await prisma.$connect();
app.decorate('prisma', prisma);


app.addHook('onClose', async (instance) => {
	// Suppress type error for custom Fastify property
	await (instance as any).prisma.$disconnect();
});
});


declare module 'fastify' {
interface FastifyInstance {
prisma: PrismaClient;
}
}