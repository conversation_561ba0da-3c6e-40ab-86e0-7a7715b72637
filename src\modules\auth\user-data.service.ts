import { Injectable, Logger } from '@nestjs/common';
import { User } from './types/auth.types';

@Injectable()
export class UserDataService {
  private readonly logger = new Logger(UserDataService.name);

  async enrichUserData(user: User): Promise<User> {
    // This service can be used to enrich user data with additional information
    // from databases or external services if needed
    this.logger.debug(`Enriching user data for: ${user.sub}`);
    
    return user;
  }

  async getUserPermissions(user: User): Promise<string[]> {
    // Extract permissions from user data or fetch from external service
    return user.permissions || [];
  }

  async getUserRoles(user: User): Promise<string[]> {
    // Extract roles from user data
    return user.role ? [user.role] : [];
  }
}
