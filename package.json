{"name": "marketplace-fastify", "version": "0.1.0", "private": true, "scripts": {"start:dev": "nest start --watch", "start:local": "dotenv -e .env -- npx nest start --watch", "start:watch": "npx tsc --noEmit --watch", "docker:build": "docker build -t auth-backend .", "docker:run": "docker run --rm --env-file .env -p 3001:3001 auth-backend", "dev:docker": "docker-compose -f ./docker-compose.yml up --build auth-backend", "start:docker": "docker-compose -f ./docker-compose.yml up auth-backend", "prisma:generate": "npx prisma generate", "prisma:migrate": "npx prisma migrate dev --name init", "docker:up": "docker-compose -f ./docker-compose.yml up -d --build", "docker:down": "docker-compose -f ./docker-compose.yml down", "start:debug": "npx nest start --debug --watch", "start:prod": "node dist/main", "clean": "rimraf ./dist", "start": "node dist/main.js", "dev": "env-cmd -f .env nodemon", "build": "npx prisma generate && npx nest build", "test": "env-cmd -f .env jest --config=jest.json", "lint": "eslint -c .eslintrc.js --ext .ts 'src/**/*.ts'", "seed:auth": "env-cmd -f .env ts-node src/scripts/seed-auth.ts", "seed": "node ./dist/prisma/prisma-seed.js", "seed:dev": "ts-node ./src/prisma/prisma-seed.ts", "migrate:generate": "npx prisma migrate dev --create-only", "migrate:up": "npx prisma migrate dev", "migrate:deploy": "npx prisma migrate deploy", "migrate:status": "npx prisma migrate status"}, "engines": {"node": ">=20.11.0"}, "devDependencies": {"@eslint/js": "^9.7.0", "@fastify/cors": "^11.1.0", "@fastify/env": "^5.0.2", "@fastify/helmet": "^13.0.1", "@fastify/jwt": "^10.0.0", "@fastify/sensible": "^6.0.3", "@fastify/static": "^8.2.0", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.3", "@nestjs/cli": "^11.0.10", "@nestjs/testing": "^11.1.6", "@prisma/client": "^6.15.0", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.3.0", "env-cmd": "^11.0.0", "eslint": "^9.34.0", "fastify": "^5.5.0", "fastify-type-provider-zod": "^6.0.0", "graphql": "^16.11.0", "graphql-type-json": "^0.3.2", "nodemon": "^3.1.10", "prisma": "^6.15.0", "tsx": "^4.20.5", "typescript": "^5.9.2", "typescript-eslint": "^8.42.0", "zod": "^4.1.5"}, "dependencies": {"@fastify/cookie": "^11.0.2", "@fastify/rate-limit": "^10.3.0", "@nestjs/axios": "^4.0.1", "@nestjs/common": "11.1.6", "@nestjs/config": "^4.0.2", "@nestjs/core": "11.1.6", "@nestjs/graphql": "^13.1.0", "@nestjs/mercurius": "^13.1.0", "@nestjs/platform-fastify": "^11.1.6", "@nestjs/swagger": "^11.2.0", "axios": "^1.12.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "handlebars": "^4.7.8", "ioredis": "^5.4.1", "joi": "^18.0.1", "jsonwebtoken": "^9.0.2", "mercurius": "^16.2.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2"}}