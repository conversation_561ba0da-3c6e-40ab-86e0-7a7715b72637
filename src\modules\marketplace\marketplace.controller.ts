import { Body, Controller, Get, Param, Patch, Post, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody, ApiBadRequestResponse, ApiNotFoundResponse } from '@nestjs/swagger';
import { MarketplaceService } from './marketplace.service';
import { CreateAppDto, UpdateAppDto } from './dto/app.dto';

@ApiTags('Marketplace')
@Controller('marketplace')
export class MarketplaceController {
  constructor(private svc: MarketplaceService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new marketplace application',
    description: 'Creates a new application in the marketplace with the provided details'
  })
  @ApiBody({ type: CreateAppDto })
  @ApiResponse({
    status: 201,
    description: 'Application created successfully'
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data'
  })
  create(@Body() dto: CreateAppDto) {
    return this.svc.create(dto);
  }

  @Get()
  @ApiOperation({
    summary: 'List marketplace applications',
    description: 'Retrieve a paginated list of marketplace applications'
  })
  @ApiQuery({
    name: 'cursor',
    required: false,
    description: 'Pagination cursor for next page'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    example: 20
  })
  @ApiResponse({
    status: 200,
    description: 'List of applications retrieved successfully'
  })
  list(
    @Query('cursor') cursor?: string,
    @Query('limit') limit = 20
  ) {
    return this.svc.list(cursor, Number(limit));
  }

  @Get(':appId')
  @ApiOperation({
    summary: 'Get application by ID',
    description: 'Retrieve a specific marketplace application by its ID'
  })
  @ApiParam({
    name: 'appId',
    description: 'Application ID'
  })
  @ApiResponse({
    status: 200,
    description: 'Application retrieved successfully'
  })
  @ApiNotFoundResponse({
    description: 'Application not found'
  })
  get(@Param('appId') id: string) {
    return this.svc.get(id);
  }

  @Patch(':appId')
  @ApiOperation({
    summary: 'Update application',
    description: 'Update an existing marketplace application'
  })
  @ApiParam({
    name: 'appId',
    description: 'Application ID'
  })
  @ApiBody({ type: UpdateAppDto })
  @ApiResponse({
    status: 200,
    description: 'Application updated successfully'
  })
  @ApiNotFoundResponse({
    description: 'Application not found'
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data'
  })
  update(
    @Param('appId') id: string,
    @Body() dto: UpdateAppDto
  ) {
    return this.svc.update(id, dto);
  }
}
