const SENSITIVE_HEADERS = ['authorization', 'x-api-key', 'set-cookie'];
const SENSITIVE_KEYS = ['client_secret', 'password', 'token', 'access_token', 'refresh_token'];


export function redact(value: any): any {
if (value == null) return value;
if (Array.isArray(value)) return value.map(redact);
if (typeof value === 'object') {
return Object.fromEntries(
Object.entries(value).map(([k, v]) => [
k,
SENSITIVE_KEYS.includes(k.toLowerCase()) ? '***redacted***' : redact(v),
]),
);
}
return value;
}


export function redactHeaders(headers: Record<string, any>) {
const out: Record<string, any> = {};
for (const [k, v] of Object.entries(headers || {})) {
out[k] = SENSITIVE_HEADERS.includes(k.toLowerCase()) ? '***redacted***' : v;
}
return out;
}