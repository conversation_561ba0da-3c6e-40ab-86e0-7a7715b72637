export default () => ({
nodeEnv: process.env.NODE_ENV ?? 'dev',
port: Number(process.env.PORT ?? 3050),
databaseUrl: process.env.DATABASE_URL!,
auth: {
issuer: process.env.AUTH_ISSUER || process.env.JWT_ISSUER_URL || 'https://ng-auth-dev.dev1.ngnair.com',
audience: process.env.AUTH_AUDIENCE || process.env.JWT_AUDIENCE || 'ngnair',
jwksUri: process.env.AUTH_JWKS_URI || process.env.AUTH_JWKS_URL || 'https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks',
serviceUrl: process.env.AUTH_SERVICE_URL || 'https://ng-auth-dev.dev1.ngnair.com',
encryptionKey: process.env.ACCESS_TOKEN_ENCRYPTION_KEY || 'b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8',
skipVerification: process.env.JWT_SKIP_VERIFICATION === 'true',
cookieNames: {
accessToken: 'access_token',
refreshToken: 'refresh_token',
},
},
masterKey: process.env.MASTER_KEY!,
});