import fp from 'fastify-plugin';
import env from '@fastify/env';


const schema = {
type: 'object',
required: ['DATABASE_URL', 'JWT_SECRET', 'PORT'],
properties: {
DATABASE_URL: { type: 'string' },
JWT_SECRET: { type: 'string' },
PORT: { type: 'string', default: '3000' }
}
} as const;


export default fp(async (app) => {
	// Suppress type error for plugin registration
	await app.register(env as any, {
		dotenv: true,
		schema,
	});
});