import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { UseGuards, Logger } from '@nestjs/common';
import { MarketplaceService } from './marketplace.service';
import { App } from './entities/app.entity';
import { CreateAppInput, UpdateAppInput } from './dto/app-input.dto';
import { GraphQLAuthGuard } from '../auth/guards/graphql-auth.guard';

@Resolver(() => App)
export class MarketplaceResolver {
  private readonly logger = new Logger(MarketplaceResolver.name);

  constructor(private readonly marketplaceService: MarketplaceService) {}

  @Query(() => [App], {
    description: 'Get list of marketplace applications'
  })
  async apps(
    @Args('cursor', { nullable: true }) cursor?: string,
    @Args('limit', { defaultValue: 20 }) limit?: number
  ): Promise<App[]> {
    try {
      this.logger.log('🔍 [MARKETPLACE RESOLVER] GraphQL apps query called');
      
      const result = await this.marketplaceService.list(cursor, limit);
      
      this.logger.log(`✅ [MARKETPLACE RESOLVER] Retrieved ${result.length} apps`);
      return result;
    } catch (error) {
      this.logger.error(`❌ [MARKETPLACE RESOLVER] Failed to get apps: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  @Query(() => App, {
    description: 'Get marketplace application by ID',
    nullable: true
  })
  async app(
    @Args('id', { type: () => ID }) id: string
  ): Promise<App | null> {
    try {
      this.logger.log(`🔍 [MARKETPLACE RESOLVER] GraphQL app query for ID: ${id}`);

      const app = await this.marketplaceService.get(id);

      if (app) {
        this.logger.log(`✅ [MARKETPLACE RESOLVER] Retrieved app: ${app.name}`);
      } else {
        this.logger.log(`❌ [MARKETPLACE RESOLVER] App not found: ${id}`);
      }

      return app;
    } catch (error) {
      this.logger.error(`❌ [MARKETPLACE RESOLVER] Failed to get app: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  @Mutation(() => App, {
    description: 'Create a new marketplace application'
  })
  @UseGuards(GraphQLAuthGuard)
  async createApp(
    @Args('input') input: CreateAppInput
  ): Promise<App> {
    try {
      this.logger.log(`🔨 [MARKETPLACE RESOLVER] GraphQL createApp mutation for: ${input.name}`);
      
      const app = await this.marketplaceService.create(input);
      
      this.logger.log(`✅ [MARKETPLACE RESOLVER] Created app: ${app.name}`);
      return app;
    } catch (error) {
      this.logger.error(`❌ [MARKETPLACE RESOLVER] Failed to create app: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  @Mutation(() => App, {
    description: 'Update an existing marketplace application'
  })
  @UseGuards(GraphQLAuthGuard)
  async updateApp(
    @Args('id', { type: () => ID }) id: string,
    @Args('input') input: UpdateAppInput
  ): Promise<App> {
    try {
      this.logger.log(`🔨 [MARKETPLACE RESOLVER] GraphQL updateApp mutation for ID: ${id}`);
      
      const app = await this.marketplaceService.update(id, input);
      
      this.logger.log(`✅ [MARKETPLACE RESOLVER] Updated app: ${app.name}`);
      return app;
    } catch (error) {
      this.logger.error(`❌ [MARKETPLACE RESOLVER] Failed to update app: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }
}
