// EndpointService: handles endpoint management logic
import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class EndpointService {
  constructor(private readonly prisma: PrismaService) {}

  // Add endpoint-related methods here
  async createEndpoint(appId: string, versionId: string, dto: any) {
    const v = await this.prisma.appVersion.findFirst({ where: { id: versionId, appId } });
    if (!v) throw new Error('version_not_found');
    if (!dto.pathTemplate.startsWith('/')) throw new Error('path_must_start_with_slash');
    if (dto.bodyTemplate && Buffer.byteLength(dto.bodyTemplate, 'utf8') > 256 * 1024)
      throw new Error('template_too_large');
    return this.prisma.endpointDefinition.create({ data: { versionId, ...dto } });
  }
}
