import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { AuthService } from './auth.service';
import { UserDataService } from './user-data.service';
import { ScopeResolutionService } from './scope-resolution.service';
import { ScopeFilterService } from './scope-filter.service';
import { AuthGuard } from './auth.guard';
import { AuthMiddleware } from './auth.middleware';
import { AuthController } from './auth.controller';
import { AuthResolver } from './auth.resolver';
import { ApiGuard, AdminGuard, GraphQLAuthGuard } from './guards';
import { ScopeGuard } from './guards/scope.guard';

@Module({
  imports: [ConfigModule, HttpModule],
  providers: [
    AuthService, 
    UserDataService, 
    ScopeResolutionService, 
    ScopeFilterService, 
    AuthGuard, 
    AuthMiddleware, 
    ApiGuard, 
    AdminGuard, 
    GraphQLAuthGuard, 
    ScopeGuard, 
    AuthResolver
  ],
  controllers: [AuthController],
  exports: [
    AuthService, 
    UserDataService, 
    ScopeResolutionService, 
    ScopeFilterService, 
    AuthGuard, 
    AuthMiddleware, 
    ApiGuard, 
    AdminGuard, 
    GraphQLAuthGuard, 
    ScopeGuard, 
    AuthResolver
  ],
})
export class AuthModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthMiddleware)
      .forRoutes('*');
  }
}

// Export everything for easy importing
export * from './auth.service';
export * from './user-data.service';
export * from './scope-resolution.service';
export * from './scope-filter.service';
export { AuthGuard, Roles, Permissions, Public } from './auth.guard';
export * from './auth.middleware';
export * from './auth.controller';
export * from './auth.resolver';
export * from './guards';
export * from './guards/scope.guard';
export { getCurrentUser } from './decorators/user.decorator';
export * from './types/auth.types';
export * from './types/graphql.types';
export * from './types/scope.types';
export * from './types';
