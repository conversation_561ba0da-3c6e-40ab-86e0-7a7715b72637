#!/bin/bash
echo "🟡 Starting Prisma migration setup..."

# Set default values for environment variables
export NODE_ENV=${NODE_ENV:-development}
export PORT=${PORT:-3050}
export COOKIE_DOMAIN=${COOKIE_DOMAIN:-ng-marketplace-local.dev.dev1.ngnair.com}

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
until pg_isready -h ${DATABASE_HOST:-srv-captain--pg01-dev} -p ${DATABASE_PORT:-5432} -U ${DATABASE_USER:-marketplace-admin}; do
  echo "PostgreSQL is unavailable - sleeping"
  sleep 2
done
echo "✅ PostgreSQL is ready!"

# Wait for Redis to be ready
# echo "⏳ Waiting for Redis to be ready..."
# until redis-cli -h ${REDIS_HOST:-redis} -p ${REDIS_PORT:-6379} ping; do
#   echo "Redis is unavailable - sleeping"
#   sleep 2
# done
# echo "✅ Redis is ready!"

# Function to run Prisma migrations
run_migrations() {
  echo "🔄 Running Prisma migrations..."

  # Generate Prisma client
  echo "📦 Generating Prisma client..."
  npx prisma generate

  if [ "$NODE_ENV" = "production" ]; then
    echo "🚀 Running production migrations..."
    npx prisma migrate deploy
  else
    echo "🛠️ Running development migrations..."
    npx prisma migrate dev --name auto-migration || {
      echo "⚠️ Migration failed, trying to push schema..."
      npx prisma db push --force-reset
    }
  fi

  echo "✅ Prisma setup completed!"
}

# Function to check database connection
check_database() {
  echo "🔍 Checking database connection..."

  # Try to connect to the database
  if npx prisma db pull --force > /dev/null 2>&1; then
    echo "✅ Database connection successful!"
    return 0
  else
    echo "❌ Database connection failed!"
    return 1
  fi
}

# Function to setup database
setup_database() {
  echo "🏗️ Setting up database..."

  # Check if database exists and is accessible
  if check_database; then
    echo "📊 Database is accessible, running migrations..."
    run_migrations
  else
    echo "🆕 Database not accessible, creating and setting up..."

    # Try to create database if it doesn't exist
    echo "🔨 Creating database if needed..."
    npx prisma db push --force-reset

    # Run migrations
    run_migrations
  fi
}

# Main execution
echo "🚀 Starting marketplace-backend application..."

# Setup database
setup_database

# Verify final setup
echo "🔍 Final verification..."
if check_database; then
  echo "✅ Database setup verified successfully!"
else
  echo "❌ Database setup verification failed!"
  exit 1
fi

# Print environment info
echo "🌍 Environment: $NODE_ENV"
echo "🔌 Port: $PORT"
echo "🍪 Cookie Domain: $COOKIE_DOMAIN"

# Start your NestJS app
echo "🎯 Starting NestJS application..."
exec node dist/main.js