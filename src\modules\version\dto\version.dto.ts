import { IsArray, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class VersionEnvConfigDto {
  @IsString()
  env!: 'SANDBOX' | 'PROD';

  @IsString()
  baseUrl!: string;

  @IsOptional() @IsString()
  tokenUrl?: string;

  @IsOptional() @IsString()
  audience?: string;

  @IsOptional()
  defaultHeaders?: any;

  @IsOptional()
  defaultQuery?: any;
}

export class CreateVersionDto {
  @IsString()
  version!: string; // semver-like

  @IsString()
  authType!: string;

  @IsArray()
  oauthScopes: string[] = [];

  @IsOptional() @IsString()
  notes?: string;

  // Optional initial env configs
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => VersionEnvConfigDto)
  envs?: VersionEnvConfigDto[];
}

export class ApproveVersionDto {
  @IsString()
  actor!: string;
}

export class SubmitVersionDto {
  @IsString()
  actor!: string;
}
