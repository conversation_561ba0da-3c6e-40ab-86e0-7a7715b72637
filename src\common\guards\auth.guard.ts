import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import * as jwt from 'jsonwebtoken';


@Injectable()
export class AuthGuard implements CanActivate {
async canActivate(ctx: ExecutionContext): Promise<boolean> {
const req = ctx.switchToHttp().getRequest();
const auth = req.headers['authorization'];
if (!auth || !auth.startsWith('Bearer ')) throw new UnauthorizedException();
const token = auth.slice(7);
try {
const decoded: any = jwt.decode(token) || {};
if (decoded.aud !== (process.env.AUTH_AUDIENCE || 'marketplace')) throw new UnauthorizedException('bad_audience');
req.user = { sub: decoded.sub, scopes: decoded.scope?.split(' ') ?? [] };
return true;
} catch (e) {
throw new UnauthorizedException();
}
}
}