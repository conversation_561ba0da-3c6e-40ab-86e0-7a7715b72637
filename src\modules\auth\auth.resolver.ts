import { Resolver, Query, Context } from '@nestjs/graphql';
import { UseGuards, Logger } from '@nestjs/common';
import { GraphQLAuthGuard } from './guards/graphql-auth.guard';
import { AuthService } from './auth.service';
import { getCurrentUser } from './decorators/user.decorator';
import { User } from './entities/user.entity';

@Resolver(() => User)
export class AuthResolver {
  private readonly logger = new Logger(AuthResolver.name);

  constructor(private readonly authService: AuthService) {}

  @Query(() => User, {
    description: 'Get current authenticated user information from JWT token'
  })
  @UseGuards(GraphQLAuthGuard)
  async me(@Context() context: any): Promise<User> {
    try {
      this.logger.log('🔐 [AUTH RESOLVER] GraphQL me query called');

      const cookies = context.req.cookies || {};
      const user = await this.authService.authenticateUserFromCookies(cookies);

      this.logger.log(`✅ [AUTH RESOLVER] User authenticated: ${user.sub}`);
      return user;
    } catch (error) {
      this.logger.error(`❌ [AUTH RESOLVER] Authentication failed: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  @Query(() => [User], {
    description: 'Get all users (requires authentication)'
  })
  @UseGuards(GraphQLAuthGuard)
  async users(@Context() context: any): Promise<User[]> {
    try {
      this.logger.log('🔐 [AUTH RESOLVER] GraphQL users query called');

      const cookies = context.req.cookies || {};

      // First authenticate the current user
      await this.authService.authenticateUserFromCookies(cookies);

      // Then get all users - for now return empty array as the service returns different type
      // TODO: Implement proper user list that matches GraphQL User entity
      const users: User[] = [];

      this.logger.log(`✅ [AUTH RESOLVER] Retrieved ${users.length} users`);
      return users;
    } catch (error) {
      this.logger.error(`❌ [AUTH RESOLVER] Failed to get users: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }
}
