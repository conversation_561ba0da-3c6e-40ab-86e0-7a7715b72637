-- //z-init.d/docker-entrypoint-initdb.d.sql
-- This SQL script will be executed during the initialization of the PostgreSQL container.
-- It creates multiple databases as required by the platform.

CREATE DATABASE "auth-db";
CREATE DATABASE "partner-db";
CREATE DATABASE "marketplace-db";
CREATE DATABASE "account-db";
CREATE DATABASE "ob-db";
CREATE DATABASE "support-db";
CREATE DATABASE "finance-db";
CREATE DATABASE "customer-db"; 