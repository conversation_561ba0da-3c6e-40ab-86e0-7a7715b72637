import { Injectable, Logger } from '@nestjs/common';
import { User } from './types/auth.types';
import { ScopeFilter } from './types/scope.types';

@Injectable()
export class ScopeFilterService {
  private readonly logger = new Logger(ScopeFilterService.name);

  async getFiltersForUser(user: User, entityType: string): Promise<ScopeFilter[]> {
    this.logger.debug(`Getting filters for user ${user.sub} and entity type: ${entityType}`);
    
    const filters: ScopeFilter[] = [];

    // Example: Add user-specific filters based on entity set or other criteria
    if (user.entitySet) {
      filters.push({
        field: 'entitySet',
        operator: 'eq',
        value: user.entitySet
      });
    }

    // Add more business logic for different entity types
    switch (entityType) {
      case 'marketplace':
        // Add marketplace-specific filters
        break;
      case 'order':
        // Add order-specific filters
        break;
      default:
        // Default filters
        break;
    }

    this.logger.debug(`Generated ${filters.length} filters for user ${user.sub}`);
    return filters;
  }

  async applyFilters(data: any[], filters: ScopeFilter[]): Promise<any[]> {
    this.logger.debug(`Applying ${filters.length} filters to ${data.length} items`);
    
    if (filters.length === 0) {
      return data;
    }

    const filteredData = data.filter(item => {
      return filters.every(filter => {
        const fieldValue = item[filter.field];
        
        switch (filter.operator) {
          case 'eq':
            return fieldValue === filter.value;
          case 'in':
            return Array.isArray(filter.value) && filter.value.includes(fieldValue);
          case 'contains':
            return typeof fieldValue === 'string' && fieldValue.includes(filter.value);
          case 'startsWith':
            return typeof fieldValue === 'string' && fieldValue.startsWith(filter.value);
          case 'endsWith':
            return typeof fieldValue === 'string' && fieldValue.endsWith(filter.value);
          default:
            return true;
        }
      });
    });

    this.logger.debug(`Filtered data from ${data.length} to ${filteredData.length} items`);
    return filteredData;
  }
}
