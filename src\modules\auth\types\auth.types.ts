export interface User {
  id: string;
  sub?: string;
  email?: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  role: string;
  permissions?: string[];
  createdAt: string;
  updatedAt: string;

  phone?: string;
  password?: string;
  country?: string;
  verifiedEmail?: boolean;
  verifiedPhone?: boolean;
  totpSecret?: string;
  partnerId?: string;
  mfaEnabled?: boolean;
  active?: boolean;
  accountId?: string;
  isAdmin?: boolean;

  accounts?: any[];
  partners?: any[];
  active_accounts?: any[];
  active_partners?: any[];
  first_name?: string;
  last_name?: string;

  sessionId: string;
  entitySet: string;
  permissionVersion: number;
  authMethods: string[];
  authTime: string;
  jwtId: string;
  authorizedParty: string;

  issuer: string;
  audience: string | string[];
  expiresAt: string;
}

export interface JWTPayload {
  iss: string;
  sub: string;
  email?: string;
  aud: string | string[];
  exp: number;
  iat: number;
  jti: string;

  sid: string;
  azp: string;
  ent_set: string;
  perm_v: number;
  amr: string[];
  auth_time: number;

  username?: string;
  firstName?: string;
  lastName?: string;
  role?: string;
  permissions?: string[];
  accounts?: any[];
  partners?: any[];
  active_accounts?: any[];
  active_partners?: any[];
  first_name?: string;
  last_name?: string;
}

export interface AuthConfig {
  authJwksUrl: string;
  encryptionKey: string;
  cookieNames: {
    accessToken: string;
    refreshToken: string;
  };
  jwt?: {
    issuer?: string;
    audience?: string;
    skipVerification?: boolean;
  };
}

export interface DecryptedTokens {
  accessToken: string;
  refreshToken?: string;
}

export interface JWKSResponse {
  keys: Array<{
    kty: string;
    e: string;
    n: string;
    kid: string;
  }>;
}

export interface AuthenticatedRequest extends Request {
  user: User;
}

declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}
