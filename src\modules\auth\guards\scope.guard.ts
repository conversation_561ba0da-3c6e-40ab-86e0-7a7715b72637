import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthService } from '../auth.service';
import { ScopeResolutionService } from '../scope-resolution.service';

@Injectable()
export class ScopeGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private scopeResolutionService: ScopeResolutionService,
    private reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    if (!request.user) {
      return false;
    }

    const requiredScopes = this.reflector.getAllAndOverride<string[]>('scopes', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredScopes || requiredScopes.length === 0) {
      return true;
    }

    try {
      const hasAccess = await this.scopeResolutionService.checkAccess(
        request.user,
        requiredScopes,
        request.params
      );

      if (!hasAccess) {
        throw new ForbiddenException('Insufficient scope permissions');
      }

      return true;
    } catch (error) {
      throw new ForbiddenException('Scope validation failed');
    }
  }
}
