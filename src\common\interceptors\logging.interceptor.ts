import { Call<PERSON><PERSON>ler, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { randomUUID } from 'crypto';
import { Observable, tap } from 'rxjs';
import { redact, redactHeaders } from '../utils/redaction.util';


@Injectable()
export class LoggingInterceptor implements NestInterceptor {
intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
// Check if this is a GraphQL context
const gqlContext = GqlExecutionContext.create(context);
const isGraphQL = !!gqlContext.getContext().req;

let req: any;
let requestId: string;

if (isGraphQL) {
  // Handle GraphQL requests
  req = gqlContext.getContext().req;
  requestId = req?.headers?.['x-request-id'] || randomUUID();
  if (req) {
    req.requestId = requestId;
  }
} else {
  // Handle HTTP requests
  req = context.switchToHttp().getRequest();
  requestId = req?.headers?.['x-request-id'] || randomUUID();
  if (req) {
    req.requestId = requestId;
  }
}

const start = Date.now();

// Only log if we have a valid request object
if (req) {
  const safeReq = {
    id: requestId,
    method: req.method || 'GraphQL',
    url: req.url || '/graphql',
    headers: req.headers ? redactHeaders(req.headers) : {},
    body: req.body ? redact(req.body) : {},
  };
  // eslint-disable-next-line no-console
  console.log(JSON.stringify({ type: 'request', ...safeReq }));
}

return next.handle().pipe(
tap({
next: (resBody) => {
const ms = Date.now() - start;
// eslint-disable-next-line no-console
console.log(JSON.stringify({ type: 'response', id: requestId, ms, body: redact(resBody) }));
},
error: (err) => {
const ms = Date.now() - start;
// eslint-disable-next-line no-console
console.error(JSON.stringify({ type: 'error', id: requestId, ms, error: err?.message }));
},
}),
);
}
}