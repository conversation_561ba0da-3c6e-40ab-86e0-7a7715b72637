import { IsOptional, IsString } from 'class-validator';

export class VersionEnvConfigDto {
  @IsString()
  env!: 'DEVELOPMENT' | 'TEST' | 'SANDBOX' | 'PRODUCTION';

  @IsString()
  baseUrl!: string;

  @IsOptional() @IsString()
  tokenUrl?: string;

  @IsOptional() @IsString()
  audience?: string;

  @IsOptional()
  defaultHeaders?: any;

  @IsOptional()
  defaultQuery?: any;
}

// DTOs merged into version.dto.ts
