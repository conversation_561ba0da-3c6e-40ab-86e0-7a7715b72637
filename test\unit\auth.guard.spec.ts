import { AuthGuard } from '../../src/common/guards/auth.guard';


describe('AuthGuard', () => {
it('rejects when audience mismatches', async () => {
const guard = new AuthGuard();
const ctx: any = {
switchToHttp: () => ({ getRequest: () => ({ headers: { authorization: 'Bearer '+Buffer.from(JSON.stringify({ aud: 'wrong' })).toString('base64') } }) })
};
await expect(guard.canActivate(ctx)).rejects.toThrow();
});
});